"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { Package, ArrowLeft, Search, MapPin, Clock, CheckCircle, Truck } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"

export default function TrackParcelPage() {
  const [trackingId, setTrackingId] = useState("")
  const [trackingData, setTrackingData] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const handleTrack = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const response = await fetch(`/api/track/${trackingId}`)
      if (response.ok) {
        const data = await response.json()
        setTrackingData(data)
      } else {
        alert("Tracking ID not found")
        setTrackingData(null)
      }
    } catch (error) {
      console.error("Error:", error)
      alert("Error tracking parcel")
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "picked_up":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case "in_transit":
        return <Truck className="h-5 w-5 text-blue-600" />
      case "delivered":
        return <CheckCircle className="h-5 w-5 text-green-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "picked_up":
        return "bg-green-100 text-green-800"
      case "in_transit":
        return "bg-blue-100 text-blue-800"
      case "delivered":
        return "bg-green-100 text-green-800"
      default:
        return "bg-gray-100 text-gray-800"
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div className="flex items-center gap-2">
            <Package className="h-6 w-6 text-blue-600" />
            <span className="text-xl font-bold">Track Parcel</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Tracking Form */}
          <Card className="mb-8">
            <CardHeader>
              <CardTitle>Enter Tracking ID</CardTitle>
              <CardDescription>Enter your tracking ID to get real-time updates on your parcel</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleTrack} className="flex gap-4">
                <div className="flex-1">
                  <Label htmlFor="trackingId" className="sr-only">
                    Tracking ID
                  </Label>
                  <Input
                    id="trackingId"
                    placeholder="Enter tracking ID (e.g., QD123456789)"
                    value={trackingId}
                    onChange={(e) => setTrackingId(e.target.value)}
                    required
                  />
                </div>
                <Button type="submit" disabled={loading}>
                  <Search className="h-4 w-4 mr-2" />
                  {loading ? "Tracking..." : "Track"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Tracking Results */}
          {trackingData && (
            <div className="space-y-6">
              {/* Parcel Info */}
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle>Parcel Details</CardTitle>
                    <Badge className={getStatusColor(trackingData.status)}>
                      {trackingData.status.replace("_", " ").toUpperCase()}
                    </Badge>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid md:grid-cols-2 gap-6">
                    <div>
                      <h4 className="font-semibold mb-2">From</h4>
                      <p className="text-sm text-gray-600">{trackingData.senderName}</p>
                      <p className="text-sm text-gray-600">{trackingData.senderAddress}</p>
                    </div>
                    <div>
                      <h4 className="font-semibold mb-2">To</h4>
                      <p className="text-sm text-gray-600">{trackingData.receiverName}</p>
                      <p className="text-sm text-gray-600">{trackingData.receiverAddress}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Live Map */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Live Tracking
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-96 bg-gray-200 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <MapPin className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                      <p className="text-gray-600">Google Maps with live tracking will be displayed here</p>
                      <p className="text-sm text-gray-500 mt-2">
                        Current location: {trackingData.currentLocation || "In transit"}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Timeline */}
              <Card>
                <CardHeader>
                  <CardTitle>Delivery Timeline</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {trackingData.timeline?.map((event: any, index: number) => (
                      <div key={index} className="flex items-start gap-4">
                        {getStatusIcon(event.status)}
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <h4 className="font-medium">{event.title}</h4>
                            <span className="text-sm text-gray-500">{event.time}</span>
                          </div>
                          <p className="text-sm text-gray-600">{event.description}</p>
                          {event.location && <p className="text-xs text-gray-500 mt-1">{event.location}</p>}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Delivery Partner Info */}
              {trackingData.deliveryPartner && (
                <Card>
                  <CardHeader>
                    <CardTitle>Delivery Partner</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="font-semibold text-blue-600">
                          {trackingData.deliveryPartner.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-medium">{trackingData.deliveryPartner.name}</h4>
                        <p className="text-sm text-gray-600">{trackingData.deliveryPartner.phone}</p>
                        <p className="text-sm text-gray-600">Rating: ⭐ {trackingData.deliveryPartner.rating}/5</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>
          )}

          {/* Sample tracking for demo */}
          {!trackingData && (
            <Card>
              <CardHeader>
                <CardTitle>Try Sample Tracking</CardTitle>
                <CardDescription>Use tracking ID "QD123456789" to see a sample tracking result</CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  onClick={() => {
                    setTrackingId("QD123456789")
                    setTrackingData({
                      status: "in_transit",
                      senderName: "John Doe",
                      senderAddress: "123 Main St, Downtown",
                      receiverName: "Jane Smith",
                      receiverAddress: "456 Oak Ave, Uptown",
                      currentLocation: "Near Central Mall",
                      deliveryPartner: {
                        name: "Raj Kumar",
                        phone: "+91 98765 43210",
                        rating: 4.8,
                      },
                      timeline: [
                        {
                          status: "picked_up",
                          title: "Parcel Picked Up",
                          description: "Your parcel has been picked up from the sender",
                          time: "2:30 PM",
                          location: "123 Main St, Downtown",
                        },
                        {
                          status: "in_transit",
                          title: "In Transit",
                          description: "Your parcel is on the way to destination",
                          time: "3:15 PM",
                          location: "Near Central Mall",
                        },
                      ],
                    })
                  }}
                >
                  Load Sample Data
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  )
}
