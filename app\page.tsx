import Link from "next/link"
import { Package, MapPin, Smartphone, Users, Clock, Shield } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-blue-50 to-white">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Package className="h-8 w-8 text-blue-600" />
            <span className="text-2xl font-bold text-gray-900">QuickDeliver</span>
          </div>
          <nav className="hidden md:flex items-center gap-6">
            <Link href="/book" className="text-gray-600 hover:text-blue-600">
              Book Parcel
            </Link>
            <Link href="/track" className="text-gray-600 hover:text-blue-600">
              Track
            </Link>
            <Link href="/partner" className="text-gray-600 hover:text-blue-600">
              Become Partner
            </Link>
            <Link href="/admin" className="text-gray-600 hover:text-blue-600">
              Admin
            </Link>
          </nav>
          <div className="flex items-center gap-4">
            <Link href="/book">
              <Button>Book Now</Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4">
        <div className="container mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Lightning Fast
            <span className="text-blue-600"> Local Delivery</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
            Send parcels across your city in minutes, not hours. Track in real-time and get instant notifications.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/book">
              <Button size="lg" className="text-lg px-8 py-6">
                <Package className="mr-2 h-5 w-5" />
                Book Delivery
              </Button>
            </Link>
            <Link href="/track">
              <Button size="lg" variant="outline" className="text-lg px-8 py-6">
                <MapPin className="mr-2 h-5 w-5" />
                Track Parcel
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Why Choose QuickDeliver?</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <Clock className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>Same-Day Delivery</CardTitle>
                <CardDescription>Get your parcels delivered within hours, not days</CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <MapPin className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>Real-Time Tracking</CardTitle>
                <CardDescription>Track your parcel live on Google Maps with precise location updates</CardDescription>
              </CardHeader>
            </Card>
            <Card>
              <CardHeader>
                <Shield className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle>Secure & Insured</CardTitle>
                <CardDescription>Your parcels are fully insured and handled with utmost care</CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Service Areas */}
      <section className="py-16 px-4 bg-gray-50">
        <div className="container mx-auto">
          <h2 className="text-3xl font-bold text-center mb-12">Our Service Areas</h2>
          <div className="bg-white rounded-lg p-6 shadow-lg">
            <div className="h-96 bg-gray-200 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <MapPin className="h-16 w-16 text-blue-600 mx-auto mb-4" />
                <p className="text-gray-600">Interactive Google Maps will be displayed here</p>
                <p className="text-sm text-gray-500 mt-2">Showing all available delivery zones</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Mobile App */}
      <section className="py-16 px-4 bg-blue-600 text-white">
        <div className="container mx-auto text-center">
          <Smartphone className="h-16 w-16 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">Download Our Mobile App</h2>
          <p className="text-xl mb-8 opacity-90">Book, track, and manage your deliveries on the go</p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button size="lg" variant="secondary">
              Download for iOS
            </Button>
            <Button size="lg" variant="secondary">
              Download for Android
            </Button>
          </div>
        </div>
      </section>

      {/* Partner CTA */}
      <section className="py-16 px-4 bg-white">
        <div className="container mx-auto text-center">
          <Users className="h-16 w-16 text-blue-600 mx-auto mb-6" />
          <h2 className="text-3xl font-bold mb-4">Become a Delivery Partner</h2>
          <p className="text-xl text-gray-600 mb-8">Join our network of delivery partners and start earning today</p>
          <Link href="/partner">
            <Button size="lg">Register as Partner</Button>
          </Link>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12 px-4">
        <div className="container mx-auto">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Package className="h-6 w-6" />
                <span className="text-xl font-bold">QuickDeliver</span>
              </div>
              <p className="text-gray-400">Your trusted hyperlocal delivery partner</p>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Services</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/book">Book Parcel</Link>
                </li>
                <li>
                  <Link href="/track">Track Delivery</Link>
                </li>
                <li>Same-Day Delivery</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Partners</h3>
              <ul className="space-y-2 text-gray-400">
                <li>
                  <Link href="/partner">Become Partner</Link>
                </li>
                <li>Partner Benefits</li>
                <li>Partner Support</li>
              </ul>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Support</h3>
              <ul className="space-y-2 text-gray-400">
                <li>Help Center</li>
                <li>Contact Us</li>
                <li>Terms of Service</li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 QuickDeliver. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
