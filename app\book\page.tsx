"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { Package, ArrowLeft, MapPin, User } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

export default function BookParcelPage() {
  const [formData, setFormData] = useState({
    senderName: "",
    senderPhone: "",
    senderEmail: "",
    senderAddress: "",
    receiverName: "",
    receiverPhone: "",
    receiverAddress: "",
    parcelType: "",
    weight: "",
    dimensions: "",
    description: "",
    deliveryTime: "",
    insurance: false,
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    try {
      const response = await fetch("/api/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const result = await response.json()
        alert(`Booking successful! Your tracking ID is: ${result.trackingId}`)
        // Reset form or redirect
      } else {
        alert("Booking failed. Please try again.")
      }
    } catch (error) {
      console.error("Error:", error)
      alert("An error occurred. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div className="flex items-center gap-2">
            <Package className="h-6 w-6 text-blue-600" />
            <span className="text-xl font-bold">Book Parcel</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl">Book Your Parcel Delivery</CardTitle>
              <CardDescription>Fill in the details below to schedule your parcel pickup and delivery</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Sender Information */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <User className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">Sender Information</h3>
                  </div>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="senderName">Full Name</Label>
                      <Input
                        id="senderName"
                        value={formData.senderName}
                        onChange={(e) => setFormData({ ...formData, senderName: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="senderPhone">Phone Number</Label>
                      <Input
                        id="senderPhone"
                        type="tel"
                        value={formData.senderPhone}
                        onChange={(e) => setFormData({ ...formData, senderPhone: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="senderEmail">Email Address</Label>
                      <Input
                        id="senderEmail"
                        type="email"
                        value={formData.senderEmail}
                        onChange={(e) => setFormData({ ...formData, senderEmail: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="senderAddress">Pickup Address</Label>
                    <Textarea
                      id="senderAddress"
                      value={formData.senderAddress}
                      onChange={(e) => setFormData({ ...formData, senderAddress: e.target.value })}
                      placeholder="Enter complete pickup address with landmarks"
                      required
                    />
                  </div>
                </div>

                {/* Receiver Information */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <MapPin className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">Receiver Information</h3>
                  </div>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="receiverName">Full Name</Label>
                      <Input
                        id="receiverName"
                        value={formData.receiverName}
                        onChange={(e) => setFormData({ ...formData, receiverName: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="receiverPhone">Phone Number</Label>
                      <Input
                        id="receiverPhone"
                        type="tel"
                        value={formData.receiverPhone}
                        onChange={(e) => setFormData({ ...formData, receiverPhone: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="receiverAddress">Delivery Address</Label>
                    <Textarea
                      id="receiverAddress"
                      value={formData.receiverAddress}
                      onChange={(e) => setFormData({ ...formData, receiverAddress: e.target.value })}
                      placeholder="Enter complete delivery address with landmarks"
                      required
                    />
                  </div>
                </div>

                {/* Parcel Information */}
                <div className="space-y-4">
                  <div className="flex items-center gap-2 mb-4">
                    <Package className="h-5 w-5 text-blue-600" />
                    <h3 className="text-lg font-semibold">Parcel Information</h3>
                  </div>
                  <div className="grid md:grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="parcelType">Parcel Type</Label>
                      <Select onValueChange={(value) => setFormData({ ...formData, parcelType: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="documents">Documents</SelectItem>
                          <SelectItem value="electronics">Electronics</SelectItem>
                          <SelectItem value="clothing">Clothing</SelectItem>
                          <SelectItem value="food">Food Items</SelectItem>
                          <SelectItem value="fragile">Fragile Items</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="weight">Weight (kg)</Label>
                      <Input
                        id="weight"
                        type="number"
                        step="0.1"
                        value={formData.weight}
                        onChange={(e) => setFormData({ ...formData, weight: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="dimensions">Dimensions (L×W×H cm)</Label>
                      <Input
                        id="dimensions"
                        value={formData.dimensions}
                        onChange={(e) => setFormData({ ...formData, dimensions: e.target.value })}
                        placeholder="e.g., 30×20×10"
                        required
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                      placeholder="Brief description of the parcel contents"
                    />
                  </div>
                </div>

                {/* Delivery Options */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Delivery Options</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="deliveryTime">Preferred Delivery Time</Label>
                      <Select onValueChange={(value) => setFormData({ ...formData, deliveryTime: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="asap">ASAP (Within 2 hours)</SelectItem>
                          <SelectItem value="today">Today (Same day)</SelectItem>
                          <SelectItem value="tomorrow">Tomorrow</SelectItem>
                          <SelectItem value="scheduled">Schedule for later</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="insurance"
                      checked={formData.insurance}
                      onCheckedChange={(checked) => setFormData({ ...formData, insurance: checked as boolean })}
                    />
                    <Label htmlFor="insurance">Add insurance coverage (+₹50)</Label>
                  </div>
                </div>

                {/* Pricing */}
                <Card className="bg-blue-50">
                  <CardContent className="pt-6">
                    <div className="flex justify-between items-center mb-2">
                      <span>Base delivery charge</span>
                      <span>₹99</span>
                    </div>
                    <div className="flex justify-between items-center mb-2">
                      <span>Distance charge</span>
                      <span>₹25</span>
                    </div>
                    {formData.insurance && (
                      <div className="flex justify-between items-center mb-2">
                        <span>Insurance</span>
                        <span>₹50</span>
                      </div>
                    )}
                    <div className="border-t pt-2 flex justify-between items-center font-semibold text-lg">
                      <span>Total</span>
                      <span>₹{formData.insurance ? "174" : "124"}</span>
                    </div>
                  </CardContent>
                </Card>

                <Button type="submit" size="lg" className="w-full">
                  Book Parcel Delivery
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
