-- Initialize MongoDB Collections (for reference)
-- In production, these would be MongoDB collections

-- Bookings Collection Structure
/*
{
  _id: ObjectId,
  trackingId: String,
  senderName: String,
  senderPhone: String,
  senderEmail: String,
  senderAddress: String,
  receiverName: String,
  receiverPhone: String,
  receiverAddress: String,
  parcelType: String,
  weight: Number,
  dimensions: String,
  description: String,
  deliveryTime: String,
  insurance: Boolean,
  status: String, // pending, picked_up, in_transit, delivered, cancelled
  amount: Number,
  partnerId: String,
  createdAt: Date,
  updatedAt: Date,
  estimatedDelivery: Date,
  actualDelivery: Date,
  timeline: Array
}
*/

-- Partners Collection Structure
/*
{
  _id: ObjectId,
  partnerId: String,
  fullName: String,
  email: String,
  phone: String,
  address: String,
  city: String,
  vehicleType: String,
  vehicleNumber: String,
  licenseNumber: String,
  experience: Number,
  availability: String,
  bankAccount: String,
  ifscCode: String,
  panNumber: String,
  aadharNumber: String,
  emergencyContact: String,
  documents: {
    profilePhoto: String,
    aadharCard: String,
    panCard: String,
    drivingLicense: String,
    vehicleRC: String,
    bankPassbook: String
  },
  status: String, // pending_verification, active, inactive, suspended
  rating: Number,
  totalDeliveries: Number,
  createdAt: Date,
  verifiedAt: Date
}
*/

-- Users Collection Structure
/*
{
  _id: ObjectId,
  userId: String,
  name: String,
  email: String,
  phone: String,
  addresses: Array,
  totalOrders: Number,
  createdAt: Date,
  lastOrderAt: Date
}
*/

-- Transactions Collection Structure
/*
{
  _id: ObjectId,
  transactionId: String,
  bookingId: String,
  type: String, // payment, payout, commission
  amount: Number,
  status: String, // pending, completed, failed
  paymentMethod: String,
  partnerId: String,
  userId: String,
  createdAt: Date,
  completedAt: Date
}
*/
