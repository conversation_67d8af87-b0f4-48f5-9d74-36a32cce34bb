import { type NextRequest, NextResponse } from "next/server"

// Mock database - in production, use MongoDB
const bookings: any[] = []

export async function POST(request: NextRequest) {
  try {
    const data = await request.json()

    // Generate tracking ID
    const trackingId = `QD${Date.now()}`

    // Create booking object
    const booking = {
      id: trackingId,
      ...data,
      status: "pending",
      createdAt: new Date().toISOString(),
      estimatedDelivery: new Date(Date.now() + 2 * 60 * 60 * 1000).toISOString(), // 2 hours from now
    }

    // In production, save to MongoDB
    bookings.push(booking)

    return NextResponse.json({
      success: true,
      trackingId,
      message: "Booking created successfully",
    })
  } catch (error) {
    console.error("Booking error:", error)
    return NextResponse.json({ success: false, message: "Failed to create booking" }, { status: 500 })
  }
}

export async function GET() {
  // Return all bookings for admin
  return NextResponse.json({ bookings })
}
