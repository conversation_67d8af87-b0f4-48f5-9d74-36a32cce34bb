"use client"

import type React from "react"

import { useState } from "react"
import Link from "next/link"
import { Users, ArrowLeft, FileText, Camera, CreditCard } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

export default function PartnerRegistrationPage() {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    vehicleType: "",
    vehicleNumber: "",
    licenseNumber: "",
    experience: "",
    availability: "",
    bankAccount: "",
    ifscCode: "",
    panNumber: "",
    aadharNumber: "",
    emergencyContact: "",
    termsAccepted: false,
  })

  const [files, setFiles] = useState({
    profilePhoto: null as File | null,
    aadharCard: null as File | null,
    panCard: null as File | null,
    drivingLicense: null as File | null,
    vehicleRC: null as File | null,
    bankPassbook: null as File | null,
  })

  const handleFileChange = (field: keyof typeof files, file: File | null) => {
    setFiles((prev) => ({ ...prev, [field]: file }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    const formDataToSend = new FormData()

    // Add form fields
    Object.entries(formData).forEach(([key, value]) => {
      formDataToSend.append(key, value.toString())
    })

    // Add files
    Object.entries(files).forEach(([key, file]) => {
      if (file) {
        formDataToSend.append(key, file)
      }
    })

    try {
      const response = await fetch("/api/partners", {
        method: "POST",
        body: formDataToSend,
      })

      if (response.ok) {
        alert("Registration successful! We will review your application and contact you within 24 hours.")
        // Reset form
      } else {
        alert("Registration failed. Please try again.")
      }
    } catch (error) {
      console.error("Error:", error)
      alert("An error occurred. Please try again.")
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b">
        <div className="container mx-auto px-4 py-4 flex items-center gap-4">
          <Link href="/">
            <Button variant="ghost" size="icon">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div className="flex items-center gap-2">
            <Users className="h-6 w-6 text-blue-600" />
            <span className="text-xl font-bold">Become a Partner</span>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-4xl mx-auto">
          {/* Benefits Section */}
          <Card className="mb-8 bg-blue-50">
            <CardHeader>
              <CardTitle className="text-2xl">Join Our Delivery Network</CardTitle>
              <CardDescription>Earn money on your schedule with flexible delivery opportunities</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">₹500-1500</div>
                  <p className="text-sm">Daily Earnings</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">24/7</div>
                  <p className="text-sm">Flexible Hours</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600 mb-2">Weekly</div>
                  <p className="text-sm">Payment Cycle</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Registration Form */}
          <Card>
            <CardHeader>
              <CardTitle>Partner Registration Form</CardTitle>
              <CardDescription>
                Please fill in all the required information to join our delivery network
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-8">
                {/* Personal Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Personal Information</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fullName">Full Name *</Label>
                      <Input
                        id="fullName"
                        value={formData.fullName}
                        onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email Address *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Phone Number *</Label>
                      <Input
                        id="phone"
                        type="tel"
                        value={formData.phone}
                        onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">City *</Label>
                      <Select onValueChange={(value) => setFormData({ ...formData, city: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select city" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mumbai">Mumbai</SelectItem>
                          <SelectItem value="delhi">Delhi</SelectItem>
                          <SelectItem value="bangalore">Bangalore</SelectItem>
                          <SelectItem value="pune">Pune</SelectItem>
                          <SelectItem value="hyderabad">Hyderabad</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">Complete Address *</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => setFormData({ ...formData, address: e.target.value })}
                      required
                    />
                  </div>
                </div>

                {/* Vehicle Information */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Vehicle Information</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="vehicleType">Vehicle Type *</Label>
                      <Select onValueChange={(value) => setFormData({ ...formData, vehicleType: value })}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select vehicle" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="bike">Motorcycle/Scooter</SelectItem>
                          <SelectItem value="car">Car</SelectItem>
                          <SelectItem value="van">Van/Mini Truck</SelectItem>
                          <SelectItem value="bicycle">Bicycle</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="vehicleNumber">Vehicle Number *</Label>
                      <Input
                        id="vehicleNumber"
                        value={formData.vehicleNumber}
                        onChange={(e) => setFormData({ ...formData, vehicleNumber: e.target.value })}
                        placeholder="e.g., MH01AB1234"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="licenseNumber">Driving License Number *</Label>
                      <Input
                        id="licenseNumber"
                        value={formData.licenseNumber}
                        onChange={(e) => setFormData({ ...formData, licenseNumber: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="experience">Driving Experience (years) *</Label>
                      <Input
                        id="experience"
                        type="number"
                        value={formData.experience}
                        onChange={(e) => setFormData({ ...formData, experience: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Work Preferences */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Work Preferences</h3>
                  <div>
                    <Label htmlFor="availability">Preferred Working Hours *</Label>
                    <Select onValueChange={(value) => setFormData({ ...formData, availability: value })}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select availability" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="full-time">Full Time (8+ hours/day)</SelectItem>
                        <SelectItem value="part-time">Part Time (4-6 hours/day)</SelectItem>
                        <SelectItem value="weekends">Weekends Only</SelectItem>
                        <SelectItem value="flexible">Flexible Hours</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Bank Details */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Bank Details</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="bankAccount">Bank Account Number *</Label>
                      <Input
                        id="bankAccount"
                        value={formData.bankAccount}
                        onChange={(e) => setFormData({ ...formData, bankAccount: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="ifscCode">IFSC Code *</Label>
                      <Input
                        id="ifscCode"
                        value={formData.ifscCode}
                        onChange={(e) => setFormData({ ...formData, ifscCode: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Identity Documents */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Identity Documents</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="panNumber">PAN Number *</Label>
                      <Input
                        id="panNumber"
                        value={formData.panNumber}
                        onChange={(e) => setFormData({ ...formData, panNumber: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="aadharNumber">Aadhar Number *</Label>
                      <Input
                        id="aadharNumber"
                        value={formData.aadharNumber}
                        onChange={(e) => setFormData({ ...formData, aadharNumber: e.target.value })}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="emergencyContact">Emergency Contact *</Label>
                      <Input
                        id="emergencyContact"
                        type="tel"
                        value={formData.emergencyContact}
                        onChange={(e) => setFormData({ ...formData, emergencyContact: e.target.value })}
                        required
                      />
                    </div>
                  </div>
                </div>

                {/* Document Uploads */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">Document Uploads</h3>
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="profilePhoto">Profile Photo *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="profilePhoto"
                          type="file"
                          accept="image/*"
                          onChange={(e) => handleFileChange("profilePhoto", e.target.files?.[0] || null)}
                          required
                        />
                        <Camera className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="aadharCard">Aadhar Card *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="aadharCard"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileChange("aadharCard", e.target.files?.[0] || null)}
                          required
                        />
                        <FileText className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="panCard">PAN Card *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="panCard"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileChange("panCard", e.target.files?.[0] || null)}
                          required
                        />
                        <FileText className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="drivingLicense">Driving License *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="drivingLicense"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileChange("drivingLicense", e.target.files?.[0] || null)}
                          required
                        />
                        <FileText className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="vehicleRC">Vehicle RC *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="vehicleRC"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileChange("vehicleRC", e.target.files?.[0] || null)}
                          required
                        />
                        <FileText className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                    <div>
                      <Label htmlFor="bankPassbook">Bank Passbook/Statement *</Label>
                      <div className="flex items-center gap-2">
                        <Input
                          id="bankPassbook"
                          type="file"
                          accept="image/*,.pdf"
                          onChange={(e) => handleFileChange("bankPassbook", e.target.files?.[0] || null)}
                          required
                        />
                        <CreditCard className="h-5 w-5 text-gray-400" />
                      </div>
                    </div>
                  </div>
                </div>

                {/* Terms and Conditions */}
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="terms"
                      checked={formData.termsAccepted}
                      onCheckedChange={(checked) => setFormData({ ...formData, termsAccepted: checked as boolean })}
                      required
                    />
                    <Label htmlFor="terms" className="text-sm">
                      I agree to the Terms and Conditions and Privacy Policy *
                    </Label>
                  </div>
                </div>

                <Button type="submit" size="lg" className="w-full" disabled={!formData.termsAccepted}>
                  Submit Registration
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
