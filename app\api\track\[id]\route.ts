import { type NextRequest, NextResponse } from "next/server"

// Mock tracking data - in production, fetch from MongoDB
const mockTrackingData = {
  QD123456789: {
    id: "QD123456789",
    status: "in_transit",
    senderName: "<PERSON>",
    senderAddress: "123 Main St, Downtown",
    receiverName: "Jane Smith",
    receiverAddress: "456 Oak Ave, Uptown",
    currentLocation: "Near Central Mall",
    estimatedDelivery: "2024-01-15T16:30:00Z",
    deliveryPartner: {
      name: "<PERSON>",
      phone: "+91 98765 43210",
      rating: 4.8,
      vehicleNumber: "MH01AB1234",
    },
    timeline: [
      {
        status: "pending",
        title: "Order Placed",
        description: "Your parcel booking has been confirmed",
        time: "1:45 PM",
        location: "QuickDeliver Hub",
      },
      {
        status: "picked_up",
        title: "Parcel Picked Up",
        description: "Your parcel has been picked up from the sender",
        time: "2:30 PM",
        location: "123 Main St, Downtown",
      },
      {
        status: "in_transit",
        title: "In Transit",
        description: "Your parcel is on the way to destination",
        time: "3:15 PM",
        location: "Near Central Mall",
      },
    ],
  },
}

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const trackingId = params.id

    // In production, fetch from MongoDB
    const trackingData = mockTrackingData[trackingId as keyof typeof mockTrackingData]

    if (!trackingData) {
      return NextResponse.json({ success: false, message: "Tracking ID not found" }, { status: 404 })
    }

    return NextResponse.json(trackingData)
  } catch (error) {
    console.error("Tracking error:", error)
    return NextResponse.json({ success: false, message: "Failed to fetch tracking data" }, { status: 500 })
  }
}
