import { type NextRequest, NextResponse } from "next/server"

// Mock database - in production, use MongoDB
const partners: any[] = []

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()

    // Extract form fields
    const partnerData = {
      id: `P${Date.now()}`,
      fullName: formData.get("fullName"),
      email: formData.get("email"),
      phone: formData.get("phone"),
      address: formData.get("address"),
      city: formData.get("city"),
      vehicleType: formData.get("vehicleType"),
      vehicleNumber: formData.get("vehicleNumber"),
      licenseNumber: formData.get("licenseNumber"),
      experience: formData.get("experience"),
      availability: formData.get("availability"),
      bankAccount: formData.get("bankAccount"),
      ifscCode: formData.get("ifscCode"),
      panNumber: formData.get("panNumber"),
      aadharNumber: formData.get("aadharNumber"),
      emergencyContact: formData.get("emergencyContact"),
      status: "pending_verification",
      createdAt: new Date().toISOString(),
    }

    // Handle file uploads (in production, upload to cloud storage)
    const files = {
      profilePhoto: formData.get("profilePhoto"),
      aadharCard: formData.get("aadharCard"),
      panCard: formData.get("panCard"),
      drivingLicense: formData.get("drivingLicense"),
      vehicleRC: formData.get("vehicleRC"),
      bankPassbook: formData.get("bankPassbook"),
    }

    // In production, save to MongoDB and upload files to cloud storage
    partners.push({ ...partnerData, files })

    return NextResponse.json({
      success: true,
      partnerId: partnerData.id,
      message: "Partner registration submitted successfully",
    })
  } catch (error) {
    console.error("Partner registration error:", error)
    return NextResponse.json({ success: false, message: "Failed to register partner" }, { status: 500 })
  }
}

export async function GET() {
  // Return all partners for admin
  return NextResponse.json({ partners })
}
